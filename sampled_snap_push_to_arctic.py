import multiprocessing
from arcticdb import Arctic
import pandas as pd
import os

store = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")
month_range = "def_def"

library_map = {
    "fut_trd": store["nse/1_min/fut_snap/trd"],
    "opt_trd": store["nse/1_min/opt_snap/trd"],
    "opt_ord": store["nse/1_min/opt_snap/ord"],
    "fut_ord": store["nse/1_min/fut_snap/ord"],
}


def push_to_arctic(sym):
    print(f"Started for {sym}")
    
    try:
        for data_type in os.listdir(
            f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_sampled/{sym}"
        ):
            dates = os.listdir(
                f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_sampled/{sym}/{data_type}"
            )

            dates = [pd.to_datetime(date.split(".")[0]) for date in dates]
            dates = sorted(dates)

            df_list = []
            for date in dates:
                df = pd.read_parquet(
                    f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_sampled/{sym}/{data_type}/{date.strftime('%Y-%m-%d')}.parquet"
                )
                df_list.append(df)

            df = pd.concat(df_list)
        
            if library_map[data_type].has_symbol(sym):
                last_date = library_map[data_type].get_description(sym).date_range[1]
                first_date = library_map[data_type].get_description(sym).date_range[0]

                if last_date.date() < df.index[0].date():
                    library_map[data_type].append(sym, df, prune_previous_versions=True)
                else:
                    df_old = library_map[data_type].read(sym).data
                    df = pd.concat([df_old, df]).reset_index()
                    df = df.drop_duplicates().set_index("timestamp").sort_index()
                    library_map[data_type].write(sym, df, prune_previous_versions=True)
            else:
                library_map[data_type].write(sym, df, prune_previous_versions=True)
        
            for date in dates:
                os.remove(
                    f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_sampled/{sym}/{data_type}/{date.strftime('%Y-%m-%d')}.parquet"
                )
    except Exception as e:
        print(f"Failed for {sym} due to: {e}\n")
        
    print(f"Done for {sym}")



# push_to_arctic(sym="ACC")

syms = os.listdir(
    f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_sampled"
)

with multiprocessing.Pool(10) as P:
    P.map(push_to_arctic, syms)

print()