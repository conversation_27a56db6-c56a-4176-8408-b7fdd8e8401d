use pyo3::prelude::*;
use pyo3::types::PyDict;
use polars::prelude::*;
use pyo3_polars::PyDataFrame;


/// Fast chunk data processing using Polars for vectorized operations
#[pyfunction]
fn process_chunk_data_fast(py: Python, py_df: PyDataFrame) -> PyResult<&PyDict> {
    let df = py_df.0.clone();

    // Use Polars' vectorized operations - much faster than row-by-row processing
    let lazy_df = df.lazy();

    // Separate futures and options using vectorized operations
    let df_fut = lazy_df
        .clone()
        .filter(col("strike_price").is_null())
        .drop_columns(&["strike_price", "option_type"]);

    let df_opt = lazy_df
        .clone()
        .filter(col("strike_price").is_not_null());

    // Process futures trade data (vectorized filtering)
    let df_fut_trd = df_fut
        .clone()
        .filter(col("close").neq(lit(0.0)))
        .drop_columns(&["ord_price"]);

    // Process futures order data (vectorized filtering)
    let df_fut_ord = df_fut
        .clone()
        .filter(col("ord_price").neq(lit(0.0)))
        .drop_columns(&["close", "volume"]);

    // Process options trade data (vectorized filtering)
    let df_opt_trd = df_opt
        .clone()
        .filter(col("close").neq(lit(0.0)))
        .drop_columns(&["ord_price"]);

    // Process options order data (vectorized filtering)
    let df_opt_ord = df_opt
        .clone()
        .filter(col("ord_price").neq(lit(0.0)))
        .drop_columns(&["close", "volume"]);

    // Collect results (this is where the actual computation happens)
    let fut_trd_result = df_fut_trd.collect().map_err(|e| {
        PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Error processing fut_trd: {}", e))
    })?;

    let fut_ord_result = df_fut_ord.collect().map_err(|e| {
        PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Error processing fut_ord: {}", e))
    })?;

    let opt_trd_result = df_opt_trd.collect().map_err(|e| {
        PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Error processing opt_trd: {}", e))
    })?;

    let opt_ord_result = df_opt_ord.collect().map_err(|e| {
        PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Error processing opt_ord: {}", e))
    })?;

    // Create result dictionary
    let result = PyDict::new(py);
    result.set_item("fut_trd", PyDataFrame(fut_trd_result).into_py(py))?;
    result.set_item("fut_ord", PyDataFrame(fut_ord_result).into_py(py))?;
    result.set_item("opt_trd", PyDataFrame(opt_trd_result).into_py(py))?;
    result.set_item("opt_ord", PyDataFrame(opt_ord_result).into_py(py))?;

    Ok(result)
}



/// Fast futures trade resampling using Polars groupby_dynamic
#[pyfunction]
fn resample_futures_trade_fast(py_df: PyDataFrame, interval_minutes: i64) -> PyResult<PyDataFrame> {
    let df = py_df.0.clone();

    // Use Polars' optimized groupby_dynamic for time-based resampling
    let result = df
        .lazy()
        .group_by_dynamic(
            col("timestamp"),
            [col("symbol"), col("expiry")],
            DynamicGroupOptions {
                every: Duration::parse(&format!("{}m", interval_minutes)),
                period: Duration::parse(&format!("{}m", interval_minutes)),
                offset: Duration::parse("0s"),
                include_boundaries: false,
                closed_window: ClosedWindow::Right,
                start_by: StartBy::WindowBound,
                check_sorted: true,
                ..Default::default()
            }
        )
        .agg([
            col("close").first().alias("open"),
            col("close").max().alias("high"),
            col("close").min().alias("low"),
            col("close").last().alias("close"),
            col("volume").sum().alias("volume"),
        ])
        .collect()
        .map_err(|e| {
            PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Resampling error: {}", e))
        })?;

    Ok(PyDataFrame(result))
}

/// Fast futures order resampling using Polars
#[pyfunction]
fn resample_futures_order_fast(py_df: PyDataFrame, interval_minutes: i64) -> PyResult<PyDataFrame> {
    let df = py_df.0.clone();

    let result = df
        .lazy()
        .group_by_dynamic(
            col("timestamp"),
            [col("symbol"), col("expiry")],
            DynamicGroupOptions {
                every: Duration::parse(&format!("{}m", interval_minutes)),
                period: Duration::parse(&format!("{}m", interval_minutes)),
                offset: Duration::parse("0s"),
                include_boundaries: false,
                closed_window: ClosedWindow::Right,
                start_by: StartBy::WindowBound,
                check_sorted: true,
                ..Default::default()
            }
        )
        .agg([
            col("ord_price").first().alias("open"),
            col("ord_price").max().alias("high"),
            col("ord_price").min().alias("low"),
            col("ord_price").last().alias("close"),
        ])
        .collect()
        .map_err(|e| {
            PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Resampling error: {}", e))
        })?;

    Ok(PyDataFrame(result))
}

/// Fast options trade resampling using Polars
#[pyfunction]
fn resample_options_trade_fast(py_df: PyDataFrame, interval_minutes: i64) -> PyResult<PyDataFrame> {
    let df = py_df.0.clone();

    let result = df
        .lazy()
        .group_by_dynamic(
            col("timestamp"),
            [col("symbol"), col("expiry"), col("strike_price"), col("option_type")],
            DynamicGroupOptions {
                every: Duration::parse(&format!("{}m", interval_minutes)),
                period: Duration::parse(&format!("{}m", interval_minutes)),
                offset: Duration::parse("0s"),
                include_boundaries: false,
                closed_window: ClosedWindow::Right,
                start_by: StartBy::WindowBound,
                check_sorted: true,
                ..Default::default()
            }
        )
        .agg([
            col("close").first().alias("open"),
            col("close").max().alias("high"),
            col("close").min().alias("low"),
            col("close").last().alias("close"),
        ])
        .collect()
        .map_err(|e| {
            PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Resampling error: {}", e))
        })?;

    Ok(PyDataFrame(result))
}

/// Fast options order resampling using Polars
#[pyfunction]
fn resample_options_order_fast(py_df: PyDataFrame, interval_minutes: i64) -> PyResult<PyDataFrame> {
    let df = py_df.0.clone();

    let result = df
        .lazy()
        .group_by_dynamic(
            col("timestamp"),
            [col("symbol"), col("expiry"), col("strike_price"), col("option_type")],
            DynamicGroupOptions {
                every: Duration::parse(&format!("{}m", interval_minutes)),
                period: Duration::parse(&format!("{}m", interval_minutes)),
                offset: Duration::parse("0s"),
                include_boundaries: false,
                closed_window: ClosedWindow::Right,
                start_by: StartBy::WindowBound,
                check_sorted: true,
                ..Default::default()
            }
        )
        .agg([
            col("ord_price").first().alias("open"),
            col("ord_price").max().alias("high"),
            col("ord_price").min().alias("low"),
            col("ord_price").last().alias("close"),
        ])
        .collect()
        .map_err(|e| {
            PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Resampling error: {}", e))
        })?;

    Ok(PyDataFrame(result))
}

/// Combined processing: chunk data and resample in one operation
/// This eliminates multiple pandas-polars conversions and index operations
#[pyfunction]
fn process_and_resample_chunk_fast(py: Python, py_df: PyDataFrame, interval_minutes: i64) -> PyResult<&PyDict> {
    let df = py_df.0.clone();

    // Check if this is futures-only data (no strike_price and option_type columns)
    let schema = df.schema();
    let has_strike_price = schema.contains("strike_price");
    let has_option_type = schema.contains("option_type");
    let is_futures_only = !has_strike_price && !has_option_type;

    // Use Polars' vectorized operations - much faster than row-by-row processing
    let lazy_df = df.lazy();

    if is_futures_only {
        // Handle futures-only data - no need to separate futures and options
        let df_fut = lazy_df.clone();

        // Process and resample futures trade data in one go
        let df_fut_trd_resampled = df_fut
            .clone()
            .filter(col("close").neq(lit(0.0)))
            .drop_columns(&["ord_price"])
            .group_by_dynamic(
                col("timestamp"),
                [col("symbol"), col("expiry")],
                DynamicGroupOptions {
                    every: Duration::parse(&format!("{}m", interval_minutes)),
                    period: Duration::parse(&format!("{}m", interval_minutes)),
                    offset: Duration::parse("0s"),
                    include_boundaries: false,
                    closed_window: ClosedWindow::Right,
                    start_by: StartBy::WindowBound,
                    check_sorted: true,
                    ..Default::default()
                }
            )
            .agg([
                col("close").first().alias("open"),
                col("close").max().alias("high"),
                col("close").min().alias("low"),
                col("close").last().alias("close"),
                col("volume").sum().alias("volume"),
            ]);

        // Process and resample futures order data in one go
        let df_fut_ord_resampled = df_fut
            .clone()
            .filter(col("ord_price").neq(lit(0.0)))
            .drop_columns(&["close", "volume"])
            .group_by_dynamic(
                col("timestamp"),
                [col("symbol"), col("expiry")],
                DynamicGroupOptions {
                    every: Duration::parse(&format!("{}m", interval_minutes)),
                    period: Duration::parse(&format!("{}m", interval_minutes)),
                    offset: Duration::parse("0s"),
                    include_boundaries: false,
                    closed_window: ClosedWindow::Right,
                    start_by: StartBy::WindowBound,
                    check_sorted: true,
                    ..Default::default()
                }
            )
            .agg([
                col("ord_price").first().alias("open"),
                col("ord_price").max().alias("high"),
                col("ord_price").min().alias("low"),
                col("ord_price").last().alias("close"),
            ]);

        // Collect futures results
        let fut_trd_result = df_fut_trd_resampled.collect().map_err(|e| {
            PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Error processing fut_trd: {}", e))
        })?;

        let fut_ord_result = df_fut_ord_resampled.collect().map_err(|e| {
            PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Error processing fut_ord: {}", e))
        })?;

        // Create empty DataFrames for options since there are none
        let empty_opt_schema = Schema::from_iter([
            ("timestamp".to_string(), DataType::Datetime(TimeUnit::Milliseconds, None)),
            ("symbol".to_string(), DataType::Utf8),
            ("expiry".to_string(), DataType::Utf8),
            ("strike_price".to_string(), DataType::Float64),
            ("option_type".to_string(), DataType::Utf8),
            ("open".to_string(), DataType::Float64),
            ("high".to_string(), DataType::Float64),
            ("low".to_string(), DataType::Float64),
            ("close".to_string(), DataType::Float64),
        ]);
        let empty_opt_df = DataFrame::empty_with_schema(&empty_opt_schema).map_err(|e| {
            PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Error creating empty options DataFrame: {}", e))
        })?;

        // Create result dictionary with futures data and empty options data
        let result = PyDict::new(py);
        result.set_item("fut_trd", PyDataFrame(fut_trd_result).into_py(py))?;
        result.set_item("fut_ord", PyDataFrame(fut_ord_result).into_py(py))?;
        result.set_item("opt_trd", PyDataFrame(empty_opt_df.clone()).into_py(py))?;
        result.set_item("opt_ord", PyDataFrame(empty_opt_df).into_py(py))?;

        return Ok(result);
    }

    // Original logic for mixed futures and options data
    // Separate futures and options using vectorized operations
    let df_fut = lazy_df
        .clone()
        .filter(col("strike_price").is_null())
        .drop_columns(&["strike_price", "option_type"]);

    let df_opt = lazy_df
        .clone()
        .filter(col("strike_price").is_not_null());

    // Process and resample futures trade data in one go
    let df_fut_trd_resampled = df_fut
        .clone()
        .filter(col("close").neq(lit(0.0)))
        .drop_columns(&["ord_price"])
        .group_by_dynamic(
            col("timestamp"),
            [col("symbol"), col("expiry")],
            DynamicGroupOptions {
                every: Duration::parse(&format!("{}m", interval_minutes)),
                period: Duration::parse(&format!("{}m", interval_minutes)),
                offset: Duration::parse("0s"),
                include_boundaries: false,
                closed_window: ClosedWindow::Right,
                start_by: StartBy::WindowBound,
                check_sorted: true,
                ..Default::default()
            }
        )
        .agg([
            col("close").first().alias("open"),
            col("close").max().alias("high"),
            col("close").min().alias("low"),
            col("close").last().alias("close"),
            col("volume").sum().alias("volume"),
        ]);

    // Process and resample futures order data in one go
    let df_fut_ord_resampled = df_fut
        .clone()
        .filter(col("ord_price").neq(lit(0.0)))
        .drop_columns(&["close", "volume"])
        .group_by_dynamic(
            col("timestamp"),
            [col("symbol"), col("expiry")],
            DynamicGroupOptions {
                every: Duration::parse(&format!("{}m", interval_minutes)),
                period: Duration::parse(&format!("{}m", interval_minutes)),
                offset: Duration::parse("0s"),
                include_boundaries: false,
                closed_window: ClosedWindow::Right,
                start_by: StartBy::WindowBound,
                check_sorted: true,
                ..Default::default()
            }
        )
        .agg([
            col("ord_price").first().alias("open"),
            col("ord_price").max().alias("high"),
            col("ord_price").min().alias("low"),
            col("ord_price").last().alias("close"),
        ]);

    // Process and resample options trade data in one go
    let df_opt_trd_resampled = df_opt
        .clone()
        .filter(col("close").neq(lit(0.0)))
        .drop_columns(&["ord_price"])
        .group_by_dynamic(
            col("timestamp"),
            [col("symbol"), col("expiry"), col("strike_price"), col("option_type")],
            DynamicGroupOptions {
                every: Duration::parse(&format!("{}m", interval_minutes)),
                period: Duration::parse(&format!("{}m", interval_minutes)),
                offset: Duration::parse("0s"),
                include_boundaries: false,
                closed_window: ClosedWindow::Right,
                start_by: StartBy::WindowBound,
                check_sorted: true,
                ..Default::default()
            }
        )
        .agg([
            col("close").first().alias("open"),
            col("close").max().alias("high"),
            col("close").min().alias("low"),
            col("close").last().alias("close"),
        ]);

    // Process and resample options order data in one go
    let df_opt_ord_resampled = df_opt
        .clone()
        .filter(col("ord_price").neq(lit(0.0)))
        .drop_columns(&["close", "volume"])
        .group_by_dynamic(
            col("timestamp"),
            [col("symbol"), col("expiry"), col("strike_price"), col("option_type")],
            DynamicGroupOptions {
                every: Duration::parse(&format!("{}m", interval_minutes)),
                period: Duration::parse(&format!("{}m", interval_minutes)),
                offset: Duration::parse("0s"),
                include_boundaries: false,
                label: Label::Right,
                closed_window: ClosedWindow::Right,
                start_by: StartBy::WindowBound,
                check_sorted: true,
                ..Default::default()
            }
        )
        .agg([
            col("ord_price").first().alias("open"),
            col("ord_price").max().alias("high"),
            col("ord_price").min().alias("low"),
            col("ord_price").last().alias("close"),
        ]);

    // Collect all results (this is where the actual computation happens)
    let fut_trd_result = df_fut_trd_resampled.collect().map_err(|e| {
        PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Error processing fut_trd: {}", e))
    })?;

    let fut_ord_result = df_fut_ord_resampled.collect().map_err(|e| {
        PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Error processing fut_ord: {}", e))
    })?;

    let opt_trd_result = df_opt_trd_resampled.collect().map_err(|e| {
        PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Error processing opt_trd: {}", e))
    })?;

    let opt_ord_result = df_opt_ord_resampled.collect().map_err(|e| {
        PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Error processing opt_ord: {}", e))
    })?;

    // Create result dictionary with resampled data
    let result = PyDict::new(py);
    result.set_item("fut_trd", PyDataFrame(fut_trd_result).into_py(py))?;
    result.set_item("fut_ord", PyDataFrame(fut_ord_result).into_py(py))?;
    result.set_item("opt_trd", PyDataFrame(opt_trd_result).into_py(py))?;
    result.set_item("opt_ord", PyDataFrame(opt_ord_result).into_py(py))?;

    Ok(result)
}

/// A Python module implemented in Rust for high-performance data processing
#[pymodule]
fn rust_data_processor(_py: Python, m: &PyModule) -> PyResult<()> {
    m.add_function(wrap_pyfunction!(process_chunk_data_fast, m)?)?;
    m.add_function(wrap_pyfunction!(resample_futures_trade_fast, m)?)?;
    m.add_function(wrap_pyfunction!(resample_futures_order_fast, m)?)?;
    m.add_function(wrap_pyfunction!(resample_options_trade_fast, m)?)?;
    m.add_function(wrap_pyfunction!(resample_options_order_fast, m)?)?;
    m.add_function(wrap_pyfunction!(process_and_resample_chunk_fast, m)?)?;
    Ok(())
}
