{"$message_type":"diagnostic","message":"the trait bound `polars::prelude::Field: From<(String, polars::prelude::DataType)>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src/lib.rs","byte_start":11290,"byte_end":11296,"line_start":302,"line_end":302,"column_start":32,"column_end":38,"is_primary":true,"text":[{"text":"        let empty_opt_schema = Schema::from_iter([","highlight_start":32,"highlight_end":38}],"label":"the trait `From<(String, polars::prelude::DataType)>` is not implemented for `polars::prelude::Field`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the following other types implement trait `From<T>`:\n  `polars::prelude::Field` implements `From<&AnyValue<'_>>`\n  `polars::prelude::Field` implements `From<&ArrowField>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required for `(String, polars::prelude::DataType)` to implement `Into<polars::prelude::Field>`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"required for `polars::prelude::Schema` to implement `FromIterator<(String, polars::prelude::DataType)>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `polars::prelude::Field: From<(String, polars::prelude::DataType)>` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/lib.rs:302:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m302\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let empty_opt_schema = Schema::from_iter([\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `From<(String, polars::prelude::DataType)>` is not implemented for `polars::prelude::Field`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `From<T>`:\u001b[0m\n\u001b[0m              `polars::prelude::Field` implements `From<&AnyValue<'_>>`\u001b[0m\n\u001b[0m              `polars::prelude::Field` implements `From<&ArrowField>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: required for `(String, polars::prelude::DataType)` to implement `Into<polars::prelude::Field>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: required for `polars::prelude::Schema` to implement `FromIterator<(String, polars::prelude::DataType)>`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `empty_with_schema` found for struct `polars::prelude::DataFrame` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/lib.rs","byte_start":11883,"byte_end":11900,"line_start":313,"line_end":313,"column_start":39,"column_end":56,"is_primary":true,"text":[{"text":"        let empty_opt_df = DataFrame::empty_with_schema(&empty_opt_schema).map_err(|e| {","highlight_start":39,"highlight_end":56}],"label":"function or associated item not found in `DataFrame`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `polars::prelude::DataFrame` consider using one of the following associated functions:\nrow::dataframe::<impl polars::prelude::DataFrame>::from_rows_and_schema\nrow::dataframe::<impl polars::prelude::DataFrame>::from_rows_iter_and_schema\nrow::dataframe::<impl polars::prelude::DataFrame>::try_from_rows_iter_and_schema\nrow::dataframe::<impl polars::prelude::DataFrame>::from_rows\nand 3 others","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/polars-core-0.35.4/src/frame/row/dataframe.rs","byte_start":1564,"byte_end":1644,"line_start":43,"line_end":43,"column_start":5,"column_end":85,"is_primary":true,"text":[{"text":"    pub fn from_rows_and_schema(rows: &[Row], schema: &Schema) -> PolarsResult<Self> {","highlight_start":5,"highlight_end":85}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/polars-core-0.35.4/src/frame/row/dataframe.rs","byte_start":1919,"byte_end":2061,"line_start":49,"line_end":51,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"    pub fn from_rows_iter_and_schema<'a, I>(mut rows: I, schema: &Schema) -> PolarsResult<Self>","highlight_start":5,"highlight_end":96},{"text":"    where","highlight_start":1,"highlight_end":10},{"text":"        I: Iterator<Item = &'a Row<'a>>,","highlight_start":1,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/polars-core-0.35.4/src/frame/row/dataframe.rs","byte_start":3357,"byte_end":3517,"line_start":91,"line_end":93,"column_start":5,"column_end":55,"is_primary":true,"text":[{"text":"    pub fn try_from_rows_iter_and_schema<'a, I>(mut rows: I, schema: &Schema) -> PolarsResult<Self>","highlight_start":5,"highlight_end":100},{"text":"    where","highlight_start":1,"highlight_end":10},{"text":"        I: Iterator<Item = PolarsResult<&'a Row<'a>>>,","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/polars-core-0.35.4/src/frame/row/dataframe.rs","byte_start":4797,"byte_end":4849,"line_start":133,"line_end":133,"column_start":5,"column_end":57,"is_primary":true,"text":[{"text":"    pub fn from_rows(rows: &[Row]) -> PolarsResult<Self> {","highlight_start":5,"highlight_end":57}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is a method `select_with_schema` with a similar name, but with different arguments","code":null,"level":"help","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/polars-core-0.35.4/src/frame/mod.rs","byte_start":49073,"byte_end":49235,"line_start":1441,"line_end":1444,"column_start":5,"column_end":23,"is_primary":true,"text":[{"text":"    pub fn select_with_schema<I, S>(&self, selection: I, schema: &SchemaRef) -> PolarsResult<Self>","highlight_start":5,"highlight_end":1},{"text":"    where","highlight_start":1,"highlight_end":1},{"text":"        I: IntoIterator<Item = S>,","highlight_start":1,"highlight_end":1},{"text":"        S: AsRef<str>,","highlight_start":1,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no function or associated item named `empty_with_schema` found for struct `polars::prelude::DataFrame` in the current scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/lib.rs:313:39\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m313\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let empty_opt_df = DataFrame::empty_with_schema(&empty_opt_schema).map_err(|e| {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `DataFrame`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `polars::prelude::DataFrame` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      row::dataframe::<impl polars::prelude::DataFrame>::from_rows_and_schema\u001b[0m\n\u001b[0m      row::dataframe::<impl polars::prelude::DataFrame>::from_rows_iter_and_schema\u001b[0m\n\u001b[0m      row::dataframe::<impl polars::prelude::DataFrame>::try_from_rows_iter_and_schema\u001b[0m\n\u001b[0m      row::dataframe::<impl polars::prelude::DataFrame>::from_rows\u001b[0m\n\u001b[0m      and 3 others\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/polars-core-0.35.4/src/frame/row/dataframe.rs:43:5\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m43\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m    pub fn from_rows_and_schema(rows: &[Row], schema: &Schema) -> PolarsResult<Self> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m49\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn from_rows_iter_and_schema<'a, I>(mut rows: I, schema: &Schema) -> PolarsResult<Self>\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m50\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    where\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m51\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        I: Iterator<Item = &'a Row<'a>>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|________________________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m91\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn try_from_rows_iter_and_schema<'a, I>(mut rows: I, schema: &Schema) -> PolarsResult<Self>\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m92\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    where\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m93\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        I: Iterator<Item = PolarsResult<&'a Row<'a>>>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|______________________________________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m133\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m    pub fn from_rows(rows: &[Row]) -> PolarsResult<Self> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `select_with_schema` with a similar name, but with different arguments\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/polars-core-0.35.4/src/frame/mod.rs:1441:5\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1441\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn select_with_schema<I, S>(&self, selection: I, schema: &SchemaRef) -> PolarsResult<Self>\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1442\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    where\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1443\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        I: IntoIterator<Item = S>,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1444\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        S: AsRef<str>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|______________________^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 2 previous errors","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 2 previous errors\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0277, E0599.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mSome errors have detailed explanations: E0277, E0599.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0277`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about an error, try `rustc --explain E0277`.\u001b[0m\n"}
